{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}

{% block title %}Welcome to CozyWish - Get Started{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'dashboard_app/css/dashboard.css' %}">
<style>
    .onboarding-hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
        border-radius: 0.5rem;
    }
    
    .checklist-item {
        transition: all 0.3s ease;
        border-left: 4px solid #e9ecef;
        margin-bottom: 1rem;
    }
    
    .checklist-item.completed {
        border-left-color: #28a745;
        background-color: #f8fff9;
    }
    
    .checklist-item.high-priority {
        border-left-color: #dc3545;
    }
    
    .checklist-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    
    .feature-card {
        transition: all 0.3s ease;
        height: 100%;
        border: none;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .feature-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }
    
    .progress-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 1.1rem;
    }
    
    .help-resource {
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }
    
    .help-resource:hover {
        border-color: #007bff;
        transform: translateY(-1px);
    }
    
    .quick-action-btn {
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .quick-action-btn:hover {
        transform: translateY(-2px);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Welcome Hero Section -->
    <div class="onboarding-hero text-center">
        <div class="container">
            <h1 class="display-4 mb-3">
                <i class="fas fa-heart text-danger"></i> 
                Welcome to CozyWish, 
                {% if provider_profile.business_name %}
                    {{ provider_profile.business_name }}!
                {% else %}
                    {{ provider_profile.user.get_full_name|default:provider_profile.user.username }}!
                {% endif %}
            </h1>
            <p class="lead mb-4">
                You're just a few steps away from connecting with customers and growing your business. 
                Let's get you set up for success!
            </p>
            
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="progress mb-3" style="height: 20px;">
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: {{ onboarding_progress.progress_percentage }}%"
                             aria-valuenow="{{ onboarding_progress.progress_percentage }}" 
                             aria-valuemin="0" aria-valuemax="100">
                            {{ onboarding_progress.completed_steps }} of {{ onboarding_progress.total_steps }} completed
                        </div>
                    </div>
                    <p class="mb-0">
                        <strong>{{ onboarding_progress.completed_steps }}</strong> out of <strong>{{ onboarding_progress.total_steps }}</strong> steps completed
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Left Column: Checklist -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-clipboard-check text-primary"></i>
                        Getting Started Checklist
                    </h4>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-4">
                        Complete these steps to unlock all features and start accepting bookings from customers.
                    </p>
                    
                    {% for item in onboarding_checklist %}
                    <div class="checklist-item card p-3 {% if item.completed %}completed{% elif item.priority == 'high' %}high-priority{% endif %}">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                {% if item.completed %}
                                    <i class="{{ item.icon }} text-success fa-2x"></i>
                                {% else %}
                                    <i class="{{ item.icon }} text-muted fa-2x"></i>
                                {% endif %}
                            </div>
                            <div class="flex-grow-1">
                                <h5 class="mb-1">
                                    {{ item.step }}
                                    {% if item.completed %}
                                        <span class="badge bg-success ms-2">
                                            <i class="fas fa-check"></i> Completed
                                        </span>
                                    {% elif item.priority == 'high' %}
                                        <span class="badge bg-danger ms-2">Required</span>
                                    {% else %}
                                        <span class="badge bg-secondary ms-2">Optional</span>
                                    {% endif %}
                                </h5>
                                <p class="text-muted mb-2">{{ item.description }}</p>
                                {% if not item.completed and item.url %}
                                    <a href="{% url item.url %}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-arrow-right"></i> Get Started
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Feature Preview Cards -->
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-star text-warning"></i>
                        What You'll Unlock
                    </h4>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-4">
                        Here's what you'll be able to do once you complete your venue setup:
                    </p>
                    
                    <div class="row">
                        {% for feature in feature_preview %}
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="feature-card card h-100 text-center">
                                <div class="card-body">
                                    <div class="feature-icon text-{{ feature.color }}">
                                        <i class="{{ feature.icon }}"></i>
                                    </div>
                                    <h5 class="card-title">{{ feature.title }}</h5>
                                    <p class="card-text text-muted">{{ feature.description }}</p>
                                    <ul class="list-unstyled text-sm">
                                        {% for benefit in feature.benefits %}
                                        <li class="mb-1">
                                            <i class="fas fa-check text-success me-2"></i>{{ benefit }}
                                        </li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column: Help & Resources -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt text-primary"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    {% for action in help_resources.quick_actions %}
                    {% if action.url|slice:":4" == "http" %}
                        <a href="{{ action.url }}" class="btn btn-{{ action.color }} quick-action-btn w-100 mb-3" target="_blank">
                            <i class="{{ action.icon }} me-2"></i>
                            {{ action.title }}
                            <small class="d-block text-white-50">{{ action.description }}</small>
                        </a>
                    {% else %}
                        <a href="{% url action.url %}" class="btn btn-{{ action.color }} quick-action-btn w-100 mb-3">
                            <i class="{{ action.icon }} me-2"></i>
                            {{ action.title }}
                            <small class="d-block text-white-50">{{ action.description }}</small>
                        </a>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>

            <!-- Help Resources -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-question-circle text-info"></i>
                        Help & Guides
                    </h5>
                </div>
                <div class="card-body">
                    {% for guide in help_resources.guides %}
                    <div class="help-resource">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="{{ guide.icon }} text-primary fa-lg"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ guide.title }}</h6>
                                <p class="text-muted mb-0 small">{{ guide.description }}</p>
                            </div>
                            <div>
                                <a href="{{ guide.url }}" class="btn btn-outline-primary btn-sm" target="_blank">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Support Resources -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-headset text-success"></i>
                        Support
                    </h5>
                </div>
                <div class="card-body">
                    {% for support in help_resources.support %}
                    <div class="help-resource">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="{{ support.icon }} text-success fa-lg"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ support.title }}</h6>
                                <p class="text-muted mb-0 small">{{ support.description }}</p>
                            </div>
                            <div>
                                <a href="{{ support.url }}" class="btn btn-outline-success btn-sm" target="_blank">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Motivational Footer -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="text-center p-4 bg-light rounded">
                <h4 class="text-muted mb-2">
                    <i class="fas fa-heart text-danger"></i>
                    Ready to Transform Your Business?
                </h4>
                <p class="text-muted mb-3">
                    Join thousands of service providers who are already growing their business with CozyWish.
                    Your success story starts here!
                </p>
                <a href="{% url 'venues_app:venue_create_wizard_default' %}" class="btn btn-primary btn-lg">
                    <i class="fas fa-rocket me-2"></i>
                    Start Your Journey
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add some interactive elements
    document.addEventListener('DOMContentLoaded', function() {
        // Animate progress bar on load
        const progressBar = document.querySelector('.progress-bar');
        if (progressBar) {
            const width = progressBar.style.width;
            progressBar.style.width = '0%';
            setTimeout(() => {
                progressBar.style.width = width;
            }, 500);
        }
        
        // Add click tracking for checklist items
        document.querySelectorAll('.checklist-item').forEach(item => {
            item.addEventListener('click', function() {
                const button = this.querySelector('.btn');
                if (button) {
                    button.click();
                }
            });
        });
    });
</script>
{% endblock %} 